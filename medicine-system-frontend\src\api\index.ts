import axios from 'axios';
import { ElMessage } from 'element-plus';

const service = axios.create({
  baseURL: '/api',
  timeout: 10000,
});

// 请求拦截器，自动添加token
service.interceptors.request.use(
  config => {
    // 自动添加Authorization头
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// 响应拦截器，统一处理Result结构和401响应
service.interceptors.response.use(
  response => {
    const res = response.data;

    // 检查是否是标准的Result格式
    if (res && typeof res === 'object' && 'code' in res) {
      if (res.code !== 200) {
        ElMessage.error(res.msg || '请求失败');
        return Promise.reject(res);
      }
      return res.data;
    }

    // 如果不是Result格式，直接返回数据（兼容旧接口）
    return res;
  },
  error => {
    // 处理401未授权响应
    if (error.response?.status === 401) {
      // 清除本地token
      localStorage.removeItem('token');
      ElMessage.error('登录状态已过期，请重新登录');
      // 延迟跳转，避免循环依赖
      setTimeout(() => {
        window.location.href = '/login';
      }, 1000);
      return Promise.reject(error);
    }

    // 处理其他错误
    const message = error.response?.data?.msg || error.message || '网络错误';
    ElMessage.error(message);
    return Promise.reject(error);
  }
);

export default service; 