<template>
  <div class="login-container">
    <div class="login-background"></div>
    <div class="login-content">
      <div class="login-form-container">
        <div class="login-logo">
          <img src="../assets/logo.svg" alt="医药管理系统" />
        </div>
        <h2 class="login-title">医药管理系统</h2>
        <p class="login-subtitle">专业的医药管理解决方案</p>
        
        <el-form :model="form" @submit.prevent="onLogin" class="login-form">
          <el-form-item>
            <el-input 
              v-model="form.username" 
              prefix-icon="el-icon-user" 
              placeholder="请输入用户名"
            />
          </el-form-item>
          <el-form-item>
            <el-input 
              v-model="form.password" 
              prefix-icon="el-icon-lock" 
              type="password" 
              placeholder="请输入密码"
              show-password
            />
          </el-form-item>
          <div class="login-options">
            <el-checkbox v-model="rememberMe">记住我</el-checkbox>
            <a href="#" class="forgot-password">忘记密码?</a>
          </div>
          <el-form-item>
            <el-button 
              type="primary" 
              @click="onLogin" 
              :loading="loading" 
              class="login-button"
            >
              登录
            </el-button>
          </el-form-item>
          
          <div class="form-footer">
            没有账号？ <router-link to="/register" class="register-link">立即注册</router-link>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../store/auth';
import { ElMessage } from 'element-plus';

const router = useRouter();
const authStore = useAuthStore();
const loading = ref(false);
const form = ref({ username: '', password: '' });
const rememberMe = ref(false);

const onLogin = async () => {
  if (!form.value.username || !form.value.password) {
    ElMessage.warning('请输入用户名和密码');
    return;
  }
  loading.value = true;
  try {
    // 使用authStore的loginAction，它会处理token和用户信息的设置
    const success = await authStore.loginAction({
      username: form.value.username,
      password: form.value.password,
      rememberMe: rememberMe.value
    });

    if (success) {
      // 登录成功，路由守卫会自动处理跳转
      router.push('/dashboard');
    }
  } catch (e) {
    // 错误已由authStore处理
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.login-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--bg-color-secondary) 0%, var(--bg-color-tertiary) 100%);
  z-index: -1;
}

.login-background::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, var(--primary-color) 0%, transparent 60%);
  opacity: 0.1;
}

.login-background::after {
  content: '';
  position: absolute;
  bottom: -50%;
  left: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, var(--primary-color) 0%, transparent 60%);
  opacity: 0.1;
}

.login-content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 0 20px;
}

.login-form-container {
  width: 380px;
  padding: 40px;
  background-color: var(--bg-color-secondary);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  animation: fadeIn 0.5s ease;
  transition: var(--transition-all);
}

.login-form-container:hover {
  box-shadow: var(--box-shadow-hover);
}

.login-logo {
  text-align: center;
  margin-bottom: 16px;
}

.login-logo img {
  height: 64px;
  width: auto;
}

.login-title {
  color: var(--text-color-primary);
  font-size: 24px;
  font-weight: 600;
  text-align: center;
  margin: 0 0 8px;
}

.login-subtitle {
  color: var(--text-color-secondary);
  font-size: 14px;
  text-align: center;
  margin: 0 0 32px;
}

.login-form :deep(.el-input__inner) {
  height: 46px;
  border-radius: var(--border-radius-base);
  border: 1px solid var(--border-color);
}

.login-form :deep(.el-input__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.login-form :deep(.el-input__prefix) {
  left: 12px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.forgot-password {
  font-size: 14px;
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-all);
}

.forgot-password:hover {
  color: var(--primary-hover);
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  font-weight: 500;
  border-radius: var(--border-radius-base);
  margin-top: 8px;
  transition: var(--transition-all);
}

.form-footer {
  text-align: center;
  margin-top: 20px;
  font-size: 14px;
  color: var(--text-color-secondary);
}

.register-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition-all);
}

.register-link:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 适配不同屏幕尺寸 */
@media (max-width: 480px) {
  .login-form-container {
    width: 100%;
    padding: 30px 20px;
  }
}

@media (min-width: 1200px) {
  .login-form-container {
    width: 420px;
    padding: 50px;
  }
}
</style> 