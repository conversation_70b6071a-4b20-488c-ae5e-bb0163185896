<template>
  <div class="dashboard-page">
    <div class="page-header">
      <div class="header-content">
        <div class="header-text">
          <h1>🏥 医药管理系统 - 仪表盘</h1>
          <p>欢迎回来，{{ userName }}！</p>
        </div>
        <div class="header-actions">
          <el-button type="danger" plain @click="handleQuickLogout">
            <el-icon><SwitchButton /></el-icon>
            快速退出
          </el-button>
        </div>
      </div>
    </div>

    <el-card>
      <h2>🎉 恭喜！</h2>
      <p>您已成功登录医药管理系统！</p>
      <p>系统功能正在开发中...</p>

      <div class="quick-actions">
        <h3>快捷操作</h3>
        <el-button type="primary">药品管理</el-button>
        <el-button type="success">库存管理</el-button>
        <el-button type="warning">采购管理</el-button>
        <el-button type="info">销售管理</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useAuthStore } from '../../store/auth';
import { ElMessageBox } from 'element-plus';
import { SwitchButton } from '@element-plus/icons-vue';

const router = useRouter();
const authStore = useAuthStore();

// 获取用户名
const userName = computed(() => {
  return authStore.user?.username || '用户';
});

// 快速退出登录
const handleQuickLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '快速退出',
      {
        confirmButtonText: '确定退出',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }
    );

    // 调用退出登录
    await authStore.logoutAction();
    router.push('/login');
  } catch {
    // 用户取消操作
  }
};
</script>

<style scoped>
.dashboard-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-text h1 {
  color: white;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.quick-actions {
  margin-top: 20px;
  text-align: center;
}

.quick-actions .el-button {
  margin: 0 10px 10px 0;
}
</style>