2025-08-01 15:54:48.624  INFO 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 8628 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-01 15:54:48.632 DEBUG 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-01 15:54:48.633  INFO 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-01 15:54:48.700  INFO 8628 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-01 15:54:48.701  INFO 8628 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-01 15:54:49.525  INFO 8628 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01 15:54:49.620  INFO 8628 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 86 ms. Found 11 JPA repository interfaces.
2025-08-01 15:54:50.347  INFO 8628 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-01 15:54:50.359  INFO 8628 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-01 15:54:50.360  INFO 8628 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-01 15:54:50.436  INFO 8628 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-01 15:54:50.437  INFO 8628 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1736 ms
2025-08-01 15:54:50.682  INFO 8628 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01 15:54:50.748  INFO 8628 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-01 15:54:50.957  INFO 8628 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-01 15:54:51.076  INFO 8628 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01 15:54:51.597  INFO 8628 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01 15:54:51.617  INFO 8628 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-01 15:54:52.504  INFO 8628 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-01 15:54:52.519  INFO 8628 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01 15:54:52.634  WARN 8628 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-01 15:54:53.427  WARN 8628 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: b2d6fc56-2ad4-45d0-aa82-f2b99666ac23

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01 15:54:53.568  INFO 8628 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@436e4242, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@2c37cd24, org.springframework.security.web.context.SecurityContextPersistenceFilter@6e562c83, org.springframework.security.web.header.HeaderWriterFilter@23d78b44, org.springframework.web.filter.CorsFilter@5597cd67, org.springframework.security.web.authentication.logout.LogoutFilter@25c5ccb8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@692caabf, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2a33f195, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@48745cc5, org.springframework.security.web.session.SessionManagementFilter@26568ca7, org.springframework.security.web.access.ExceptionTranslationFilter@71de3b02, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@6aee9af0]
2025-08-01 15:54:53.961  INFO 8628 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-01 15:54:54.004  INFO 8628 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-01 15:54:54.014  INFO 8628 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.848 seconds (JVM running for 10.396)
2025-08-01 15:55:31.123  INFO 8628 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01 15:55:31.124  INFO 8628 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01 15:55:31.130  INFO 8628 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
